# Microservices Deployment Guide

## System Overview

This microservices system consists of 4 Docker containers:
- **formulario** - Form microservice (Streamlit app on port 8502)
- **home** - Home microservice (Streamlit app on port 8501) 
- **postgres_db** - PostgreSQL database (port 5432)
- **reverse_proxy** - Nginx reverse proxy (port 80)

## Prerequisites

- Docker and Docker Compose installed
- Python 3.11+ (for local development/testing)
- Git (for version control)

## Quick Start

### 1. Build and Start Services

```bash
cd Documentacion
docker-compose up --build
```

### 2. Verify Services

Run the test script to validate the setup:

```bash
python test_setup.py
```

### 3. Access Services

- **Form Service**: http://localhost:8502
- **Home Service**: http://localhost:8501  
- **Reverse Proxy**: http://localhost:80
- **Database**: localhost:5432

## Configuration Details

### Database Configuration

The system uses PostgreSQL with the following configuration:

**Admin User (Docker Compose)**:
- User: `admin`
- Password: `adminpass`
- Database: `formularios`

**Application User (Form Service)**:
- User: `form_user`
- Password: `una_contraseña_segura`
- Database: `formularios`
- Host: `postgres_db` (container name)

### Network Configuration

All containers communicate through a custom Docker network `microservices_network` using bridge driver.

### Environment Variables

The Form service uses these environment variables (set in docker-compose.yml):

```env
POSTGRES_HOST=postgres_db
POSTGRES_PORT=5432
POSTGRES_DB=formularios
POSTGRES_USER=form_user
POSTGRES_PASSWORD=una_contraseña_segura
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Ensure PostgreSQL container is healthy: `docker-compose ps`
   - Check logs: `docker-compose logs db`
   - Verify network connectivity: `docker network ls`

2. **Form Service Can't Connect to Database**
   - Check environment variables in container: `docker exec formulario env | grep POSTGRES`
   - Verify database user permissions: `docker exec postgres_db psql -U admin -d formularios -c "\du"`

3. **Port Conflicts**
   - Check if ports are already in use: `netstat -tulpn | grep :8502`
   - Modify port mappings in docker-compose.yml if needed

### Useful Commands

```bash
# View all container logs
docker-compose logs

# View specific service logs
docker-compose logs formulario

# Restart specific service
docker-compose restart formulario

# Access database directly
docker exec -it postgres_db psql -U admin -d formularios

# Check container health
docker-compose ps

# Rebuild and restart
docker-compose down && docker-compose up --build
```

## Development Workflow

### Making Changes

1. **Code Changes**: Edit files in `services/Form/app/` or `services/Home/app/`
2. **Database Changes**: Modify `shared/db/model.py` or `shared/db/init-form-user.sql`
3. **Configuration Changes**: Update `docker-compose.yml` or environment files

### Testing Changes

```bash
# Rebuild specific service
docker-compose build formulario

# Restart with new build
docker-compose up -d formulario

# Run validation tests
python test_setup.py
```

## Security Considerations

- Database passwords are hardcoded for development - use secrets in production
- Form user has limited database permissions (no DELETE, no DDL)
- Services communicate only through defined network
- No direct database access from outside Docker network (except for debugging)

## Next Steps

1. Implement authentication/authorization
2. Add logging and monitoring
3. Set up CI/CD pipeline
4. Configure production secrets management
5. Add health checks for all services
6. Implement data backup strategy
