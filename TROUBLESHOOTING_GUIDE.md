# Microservices Troubleshooting Guide

## Quick Fix Commands

### 1. Complete System Reset (Recommended)

```bash
cd Documentacion

# Stop and remove all containers and volumes
docker-compose down -v

# Remove any orphaned containers
docker-compose down --remove-orphans

# Rebuild and start fresh
docker-compose up --build
```

### 2. Fix Database User Issues

If you see `FATAL: role "form_user" is not permitted to log in`:

```bash
# Connect to the database container
docker exec -it postgres_db psql -U admin -d formularios

# Run the fix script
\i /docker-entrypoint-initdb.d/init-form-user.sql

# Or manually fix the user
DROP USER IF EXISTS form_user;
CREATE USER form_user WITH PASSWORD 'una_contraseña_segura' LOGIN;
GRANT CONNECT ON DATABASE formularios TO form_user;
GRANT USAGE ON SCHEMA public TO form_user;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO form_user;
```

### 3. Check Container Status

```bash
# View all container status
docker-compose ps

# View logs for specific services
docker-compose logs home
docker-compose logs formulario
docker-compose logs db

# Follow logs in real-time
docker-compose logs -f formulario
```

## Common Issues and Solutions

### Issue 1: Home Container Startup Errors

**Symptoms:**
- `.venv/bin/activate: No such file or directory`
- `grep: .env: No such file or directory`

**Solution:**
✅ **FIXED** - The `run.sh` script has been corrected and `.env` file created.

**Verification:**
```bash
docker-compose logs home | grep -E "(error|Error|ERROR)"
```

### Issue 2: Form User Login Permission

**Symptoms:**
- `FATAL: role "form_user" is not permitted to log in`
- Database connection failures from form service

**Solution:**
✅ **FIXED** - Updated SQL initialization script to explicitly enable login.

**Manual Fix if needed:**
```bash
docker exec -it postgres_db psql -U admin -d formularios -f /docker-entrypoint-initdb.d/init-form-user.sql
```

### Issue 3: Form User CREATE Table Permission

**Symptoms:**
- `permission denied for schema public`
- `Table creation failed`
- Cannot create `formularios_reporte` table

**Solution:**
✅ **FIXED** - Updated SQL initialization script to grant CREATE privileges on public schema.

**Manual Fix if needed:**
```bash
# Run the emergency fix script
docker exec -it postgres_db psql -U admin -d formularios -c "
GRANT USAGE, CREATE ON SCHEMA public TO form_user;
"

# Or run the complete fix script
docker cp fix_database_user.sql postgres_db:/tmp/
docker exec -it postgres_db psql -U admin -d formularios -f /tmp/fix_database_user.sql
```

### Issue 3: Database Initialization Script Errors

**Symptoms:**
- `relation "information_schema.schema_privileges" does not exist`
- PostgreSQL initialization fails at line 59
- Container starts but database setup incomplete

**Solution:**
✅ **FIXED** - Replaced problematic `information_schema.schema_privileges` queries with PostgreSQL 15 compatible verification methods.

**Manual verification:**
```bash
# Test the initialization script
docker exec -it postgres_db psql -U admin -d formularios -f /docker-entrypoint-initdb.d/init-form-user.sql

# Or run the test script
docker exec -it postgres_db psql -U admin -d formularios -f /tmp/test_init_script.sql
```

### Issue 4: SQLAlchemy Syntax Errors

**Symptoms:**
- `Not an executable object: 'SELECT 1'`
- `Database connection test failed`
- `Table creation failed`

**Solution:**
✅ **FIXED** - Updated database.py to use `text()` wrapper for raw SQL queries.

**Manual verification:**
```bash
# Test the fix inside the container
docker exec formulario python /app/shared/db/init_bd.py
```

### Issue 4: Database Connection Issues

**Symptoms:**
- Connection timeouts
- `could not connect to server`

**Debugging Steps:**
```bash
# Check if database is healthy
docker-compose ps db

# Test database connectivity from form container
docker exec formulario ping postgres_db

# Check database logs
docker-compose logs db

# Test direct connection
docker exec -it postgres_db psql -U admin -d formularios -c "SELECT 1;"

# Test the fixed connection function
docker exec formulario python /app/test_database_connection.py
```

## Testing Procedures

### 1. Container Health Check

```bash
# All containers should be "Up" and healthy
docker-compose ps

# Expected output:
# postgres_db    Up (healthy)
# formulario     Up
# home           Up
# reverse_proxy  Up
```

### 2. Database Connectivity Test

```bash
# Test from form container
docker exec formulario python -c "
import sys
sys.path.append('/app/shared')
from db.database import test_connection
success, message = test_connection()
print(f'Connection test: {success} - {message}')
"
```

### 3. Form Service Database Write Test

```bash
# Access form service and submit a test form
# Navigate to: http://localhost:8502
# Fill out and submit the "Registro de Reporte" form
# Check for success message: "✅ Formulario enviado y guardado exitosamente."
```

### 4. Verify Data in Database

```bash
# Connect to database and check for data
docker exec -it postgres_db psql -U admin -d formularios -c "
SELECT COUNT(*) as total_forms FROM formularios_reporte;
SELECT id, name_reporte, autor FROM formularios_reporte LIMIT 5;
"
```

## Service URLs

- **Form Service**: http://localhost:8502
- **Home Service**: http://localhost:8501
- **Reverse Proxy**: http://localhost:80
- **Database**: localhost:5432 (admin/adminpass)

## Log Locations

```bash
# Container logs
docker-compose logs [service_name]

# Application logs (inside containers)
docker exec formulario ls -la /app/logs/
docker exec home ls -la /app/logs/

# Database logs
docker-compose logs db
```

## Emergency Recovery

If all else fails:

```bash
# Nuclear option - complete cleanup
docker-compose down -v
docker system prune -f
docker volume prune -f

# Rebuild everything
docker-compose build --no-cache
docker-compose up
```

## Success Indicators

✅ All containers start without errors
✅ PostgreSQL initialization completes without SQL errors
✅ Database user `form_user` can login
✅ Form service connects to database successfully
✅ No "Not an executable object" SQLAlchemy errors
✅ No "information_schema.schema_privileges does not exist" errors
✅ Database tables are created automatically
✅ Forms can be submitted and saved
✅ No authentication errors in logs
✅ Home service starts without .env errors
