version: "3.8"

networks:
  microservices_network:
    driver: bridge

services:

  home:
    build: ./services/Home
    container_name: home
    ports:
      - "8501:8501"
    environment:
      - PYTHONUNBUFFERED=1
    volumes:
      - ./shared:/app/shared
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - microservices_network

  formulario:
    build:
      context: ./services/Form
      dockerfile: Dockerfile
    container_name: formulario
    ports:
      - "8502:8501"
    environment:
      - PYTHONUNBUFFERED=1
      - POSTGRES_HOST=postgres_db
      - POSTGRES_PORT=5432
      - POSTGRES_DB=formularios
      - POSTGRES_USER=form_user
      - POSTGRES_PASSWORD=una_contraseña_segura
    volumes:
      - form_uploads:/app/uploaded_files
      - ./shared:/app/shared
      - ./services/Form/form_schemas:/app/form_schemas
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - microservices_network

  db:
    image: postgres:15
    container_name: postgres_db
    restart: always
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: adminpass
      POSTGRES_DB: formularios
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./shared/db/init-form-user.sql:/docker-entrypoint-initdb.d/init-form-user.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d formularios"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - microservices_network

  nginx:
    image: nginx:latest
    container_name: reverse_proxy
    ports:
      - "80:80"
    volumes:
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - home
      - formulario
    restart: unless-stopped
    networks:
      - microservices_network

volumes:
  postgres_data:
  form_uploads:
