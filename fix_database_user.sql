-- Emergency fix script for form_user permissions issues
-- Run this script if the form_user cannot login or create tables

-- Connect to the formularios database as admin
\c formularios;

-- Check current form_user properties
SELECT rolname, rolcanlogin, rolsuper, rolcreatedb, rolcreaterole
FROM pg_roles
WHERE rolname = 'form_user';

-- Check current schema privileges (using alternative method)
\echo 'Checking form_user privileges...'
SELECT rolname, rolcanlogin, rolcreatedb, rolcreaterole
FROM pg_roles
WHERE rolname = 'form_user';

-- Drop and recreate the user if it exists with wrong permissions
DROP USER IF EXISTS form_user;

-- Create the user with correct permissions
CREATE USER form_user WITH
    PASSWORD 'una_contraseña_segura'
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    INHERIT;

-- Grant database connection
GRANT CONNECT ON DATABASE formularios TO form_user;

-- Grant schema usage AND CREATE privileges
GRANT USAGE, CREATE ON SCHEMA public TO form_user;

-- Grant table permissions
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO form_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO form_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT SELECT, INSERT, UPDATE ON TABLES TO form_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT USAGE, SELECT ON SEQUENCES TO form_user;

-- Verify the user can login
SELECT 'form_user login check: ' || CASE WHEN rolcanlogin THEN 'ENABLED' ELSE 'DISABLED' END as status
FROM pg_roles
WHERE rolname = 'form_user';

-- Verify user was created successfully
SELECT 'form_user status: ' ||
       CASE WHEN EXISTS (
           SELECT 1 FROM pg_roles
           WHERE rolname = 'form_user'
           AND rolcanlogin = true
       ) THEN 'EXISTS AND CAN LOGIN' ELSE 'MISSING OR CANNOT LOGIN' END as user_status;

-- Test connection and table creation
\echo 'User form_user has been recreated with login and CREATE privileges'
\echo 'You can test the connection with: psql -h postgres_db -U form_user -d formularios'
\echo 'Test table creation with: CREATE TABLE test_table (id SERIAL PRIMARY KEY);'
