server {
    listen 80;

    server_name localhost;

    # Ruta para el microservicio HOME
    location /home/<USER>
        proxy_pass http://home:8501/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_buffering off;
    }

    # Ruta para los recursos estáticos de HOME
    location /home/<USER>/ {
        proxy_pass http://home:8501/static/;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering on;
    }

    # Ruta para el microservicio FORMULARIO
    location /formulario/ {
        proxy_pass http://formulario:8501/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
        proxy_buffering off;
    }

    # Ruta para los recursos estáticos de FORMULARIO
    location /formulario/static/ {
        proxy_pass http://formulario:8501/static/;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering on;
    }

    # Ruta para los recursos estáticos en la raíz (fallback para compatibilidad)
    location /static/ {
        proxy_pass http://home:8501/static/;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering on;
    }

    # Página de bienvenida o fallback
    location / {
        return 200 'Reverse Proxy funcionando. Usa /home/<USER>/formulario/';
        add_header Content-Type text/plain;
    }
}
