# Python virtual environments
.venv/
venv/
env/
ENV/

# Environment variables and secrets
.env
*.env
.env.*

# Python bytecode and cache
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Logs
logs/*.log
*.log

# Keep logs directory
!logs/.gitkeep

# Databases
*.sqlite
*.sqlite3
*.db

# User uploaded files
uploaded_files/
!uploaded_files/.gitkeep

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# Distribution / packaging
dist/
build/
*.egg-info/
*.egg

# Docker related
*.pid
.docker/

# Streamlit specific
.streamlit/secrets.toml
.streamlit/config.toml

# Temporary files
tmp/
temp/

# Alembic migration versions (optional, you might want to keep these)
# alembic/versions/

# Jupyter Notebooks
.ipynb_checkpoints

# Local development files
local_settings.py
