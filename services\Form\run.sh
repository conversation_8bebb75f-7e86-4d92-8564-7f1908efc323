#!/bin/bash

# Activar entorno virtual si existe
if [ -d ".venv" ]; then
  source .venv/bin/activate
fi

# Exportar variables de entorno si existe un archivo .env
if [ -f ".env" ]; then
  export $(grep -v '^#' .env | xargs)
fi

# Crear directorios necesarios
mkdir -p uploaded_files
# Create logs directory with proper permissions
mkdir -p logs
chmod 755 logs

# Configurar nivel de log
export LOG_LEVEL=${LOG_LEVEL:-INFO}

# Esperar a que la base de datos esté disponible
echo "Esperando a que la base de datos esté disponible..."
python -c "
import time
import psycopg2
import os
import logging

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
"

echo "Base de datos disponible, iniciando aplicación..."

# Ejecutar migraciones de base de datos si es necesario
if [ -f "alembic.ini" ]; then
  alembic upgrade head
fi

# Ejecutar la aplicación Streamlit con logging
streamlit run app/main.py --server.port=8501 --server.address=0.0.0.0 2>&1 | tee -a logs/streamlit_$(date +%Y-%m-%d).log



