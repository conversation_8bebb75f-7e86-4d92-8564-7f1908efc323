import streamlit as st
from utils.logger import setup_logger

def render_sidebar(user=None, session_id=None):
    """
    Renders the sidebar for the Home application
    
    Parameters:
    - user: Current user name
    - session_id: Current session ID
    
    Returns:
    - selected_option: The option selected by the user
    """
    # Set up logger
    logger = setup_logger("home_service.sidebar", user=user, session_id=session_id)
    
    with st.sidebar:
        st.title("Navegación")
        
        # User info section
        st.subheader("Información de Usuario")
        st.write(f"Usuario: {user or 'Invitado'}")
        st.write(f"Sesión: {session_id[:8] + '...' if session_id and len(session_id) > 8 else session_id or 'N/A'}")
        
        # Navigation options
        st.subheader("Opciones")
        selected_option = st.radio(
            "Seleccione una opción:",
            ["Home", "Formularios", "Documentación"],
            index=0
        )
        
        # Redirect to Form service if Formularios is selected
        if selected_option == "Formularios":
            st.markdown("""
            <meta http-equiv="refresh" content="0;URL='/formulario/'">
            """, unsafe_allow_html=True)
            st.info("Redirigiendo al servicio de Formularios...")
            logger.info(f"User {user} redirected to Form service")

        if selected_option == "Documentación":
            st.markdown("""
            <meta http-equiv="refresh" content="0;URL='/docs/'">
            """, unsafe_allow_html=True)
            st.info("Redirigiendo al servicio de Documentación...")
            logger.info(f"User {user} redirected to Documentation service")
        
        # Help and support
        st.divider()
        with st.expander("Ayuda y Soporte"):
            st.write("Para obtener ayuda, contacte al administrador del sistema.")
            st.write("Email: <EMAIL>")
        
        # Version info
        st.caption("Versión 1.0.0")
    
    # Log user interaction
    logger.debug(f"User {user} selected option: {selected_option}")
    
    return {
        "selected_option": selected_option
    }
