import streamlit as st
import json
from utils.logger import setup_logger
from components.sidebar import render_sidebar

# Get user and session info
user = st.session_state.get("user", "invitado")
id_session = st.session_state.get("session_id", "xxxxxxxxx")

# Set up logger with user and session info
logger = setup_logger("home_service.main", user=user, session_id=id_session)

def main():
    logger.info(f"Application started by user {user}")
    
    # Render sidebar and get selected options
    sidebar_options = render_sidebar(user=user, session_id=id_session)
    
    # Only show home content if Home is selected
    if sidebar_options["selected_option"] == "Home":
        st.title("Bienvenido al Sistema de Documentación")
        st.write("""
        Este sistema le permite gestionar documentación y formularios.
        
        Seleccione una opción en el menú lateral para comenzar.
        """)
        
        # Display some statistics or recent activity
        st.subheader("Actividad Reciente")
        st.info("No hay actividad reciente para mostrar.")
    
    # Show placeholder for Mis Documentos
    elif sidebar_options["selected_option"] == "Formularios":
        st.info("Sección de Mis Documentos en desarrollo")
    
    logger.debug(f"Application UI rendered for user {user}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.exception(f"Unhandled exception for user {user}: {str(e)}")
        st.error("An unexpected error occurred. Please try again later.")
