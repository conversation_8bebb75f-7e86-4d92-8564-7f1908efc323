import os
import logging
import logging.handlers
from datetime import datetime

# Create logs directory if it doesn't exist
logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
os.makedirs(logs_dir, exist_ok=True)

# Configure logging
def setup_logger(name, log_level=logging.INFO, user=None, session_id=None):
    """Set up a logger with file and console handlers"""
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # Clear existing handlers to avoid duplicates
    if logger.handlers:
        logger.handlers.clear()
    
    # Create formatters with user and session info if provided
    user_info = f"| {user or 'unknown'} | {session_id or 'unknown'} "
    
    file_formatter = logging.Formatter(
        f'%(asctime)s | %(levelname)-8s | %(name)s {user_info}| %(filename)s:%(lineno)d | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_formatter = logging.Formatter(
        f'%(asctime)s | %(levelname)-8s | %(name)s {user_info}| %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Create file handler for all logs
    log_file = os.path.join(logs_dir, f'form_service_{datetime.now().strftime("%Y-%m-%d")}.log')
    file_handler = logging.handlers.RotatingFileHandler(
        log_file, maxBytes=10485760, backupCount=10, encoding='utf-8'
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(logging.DEBUG)
    
    # Create separate file handler for errors
    error_log_file = os.path.join(logs_dir, f'form_service_errors_{datetime.now().strftime("%Y-%m-%d")}.log')
    error_file_handler = logging.handlers.RotatingFileHandler(
        error_log_file, maxBytes=10485760, backupCount=10, encoding='utf-8'
    )
    error_file_handler.setFormatter(file_formatter)
    error_file_handler.setLevel(logging.ERROR)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(logging.INFO)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(error_file_handler)
    logger.addHandler(console_handler)
    
    return logger

# Example usage
# logger = setup_logger(__name__)
# logger.info("This is an info message")
# logger.error("This is an error message")
