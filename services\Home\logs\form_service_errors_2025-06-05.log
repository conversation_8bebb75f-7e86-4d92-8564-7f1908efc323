2025-06-05 15:09:19 | ERROR    | home_service.main | invitado | xxxxxxxxx | main.py:42 | Unhandled exception for user invitado: name 'selected_option' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Home\app\main.py", line 40, in <module>
    main()
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Home\app\main.py", line 17, in main
    sidebar_options = render_sidebar(user=user, session_id=id_session)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Home\app\components\sidebar.py", line 55, in render_sidebar
    "selected_option": selected_option
                       ^^^^^^^^^^^^^^^
NameError: name 'selected_option' is not defined
2025-06-05 15:09:42 | ERROR    | home_service.main | invitado | xxxxxxxxx | main.py:42 | Unhandled exception for user invitado: name 'selected_option' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Home\app\main.py", line 40, in <module>
    main()
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Home\app\main.py", line 17, in main
    sidebar_options = render_sidebar(user=user, session_id=id_session)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\11. Proyecto de documentación\Documentación\services\Home\app\components\sidebar.py", line 55, in render_sidebar
    "selected_option": selected_option
                       ^^^^^^^^^^^^^^^
NameError: name 'selected_option' is not defined
