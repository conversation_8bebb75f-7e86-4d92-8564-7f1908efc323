#!/bin/bash

# Activar entorno virtual si existe
if [ -d ".venv" ]; then
  source .venv/bin/activate
fi

# Exportar variables de entorno si existe un archivo .env
if [ -f ".env" ]; then
  export $(grep -v '^#' .env | xargs)
fi

# Crear directorios necesarios
mkdir -p logs
chmod 755 logs
# Configurar nivel de log
export LOG_LEVEL=${LOG_LEVEL:-INFO}

# Esperar a que la base de datos esté disponible si es necesario
if [ ! -z "$DB_HOST" ]; then
  echo "Esperando a que la base de datos esté disponible..."
  python -c "
import time
import psycopg2
import os
import logging
# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('db_check')

# Get database connection parameters from environment
db_host = os.environ.get('DB_HOST', 'db')
db_port = os.environ.get('DB_PORT', '5432')
db_name = os.environ.get('DB_NAME', 'formularios')
db_user = os.environ.get('DB_USER', 'admin')
db_pass = os.environ.get('DB_PASSWORD', 'adminpass')
# Try to connect to the database with retries
max_retries = 30
retry_interval = 2

for i in range(max_retries):
    try:
        logger.info(f'Attempting to connect to database (attempt {i+1}/{max_retries})...')
        conn = psycopg2.connect(
            host=db_host,
            port=db_port,
            dbname=db_name,
            user=db_user,
            password=db_pass
        )
        conn.close()
        logger.info('Successfully connected to the database!')
        break
    except psycopg2.OperationalError as e:
        logger.warning(f'Database connection failed: {e}')
        if i < max_retries - 1:
            logger.info(f'Retrying in {retry_interval} seconds...')
            time.sleep(retry_interval)
        else:
            logger.error('Max retries reached. Could not connect to the database.')
            # Continue anyway, as the Home service might work without DB
"
fi
echo "Iniciando aplicación Home..."

# Ejecutar migraciones de base de datos si es necesario
if [ -f "alembic.ini" ]; then
  alembic upgrade head
fi

# Ejecutar la aplicación Streamlit con logging
cd app
streamlit run main.py --server.port=8501 --server.address=0.0.0.0 2>&1 | tee -a ../logs/streamlit_$(date +%Y-%m-%d).log