include:
  - template: Security/SAST.gitlab-ci.yml
  - template: Security/Dependency-Scanning.gitlab-ci.yml
  - template: Security/Secret-Detection.gitlab-ci.yml

stages:
  - test
  - security

variables:
  NODE_VERSION: "22.14.0"
  PYTHON_VERSION: "3.9"
  DOCKER_TLS_CERTDIR: "/certs"
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.pip-cache"
  GIT_SSL_NO_VERIFY: "true"

.node_template: &node_template
  image: node:$NODE_VERSION
  before_script:
  - npm ci
  - mkdir -p /certs/client
  - echo "$CA_CERT" > /certs/client/ca.pem
  tags:
  - sqlfluff

# JavaScript/Node.js Jobs
js-lint:
  <<: *node_template
  stage: test
  script:
  - npm run lint
  tags:
  - sqlfluff

js-audit:
  <<: *node_template
  stage: security
  script:
  - npm audit --production
  tags:
  - sqlfluff

# Combined Security Scans
sast:
  stage: security
  script:
  - /analyzer run
  tags:
  - sqlfluff

dependency_scanning:
  stage: security
  script:
  - /analyzer run
  tags:
  - sqlfluff

secret_detection:
  stage: security
  script:
  - /analyzer run
  tags:
  - sqlfluff

# Scheduled full security scan
scheduled-security-scan:
  stage: security
  rules:
  - if: $CI_PIPELINE_SOURCE == "schedule"
  script:
  - npm audit
  - safety check
  - bandit -r .
  - /analyzer run
  tags:
  - sqlfluff