{"name": "tailwindui-protocol", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "browserslist": "defaults, not ie <= 11", "dependencies": {"@algolia/autocomplete-core": "^1.7.3", "@headlessui/react": "^2.1.0", "@mdx-js/loader": "^3.0.0", "@mdx-js/react": "^3.0.0", "@next/mdx": "^14.0.4", "@sindresorhus/slugify": "^2.1.1", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "^0.5.10", "@types/mdx": "^2.0.8", "@types/node": "^20.10.8", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@types/react-highlight-words": "^0.16.4", "acorn": "^8.8.1", "clsx": "^2.1.0", "fast-glob": "^3.3.0", "flexsearch": "^0.7.31", "framer-motion": "^10.18.0", "mdast-util-to-string": "^4.0.0", "mdx-annotations": "^0.1.1", "next": "^14.0.4", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-highlight-words": "^0.20.0", "remark": "^15.0.1", "remark-gfm": "^4.0.0", "remark-mdx": "^3.0.0", "shiki": "^0.14.7", "simple-functional-loader": "^1.2.1", "tailwindcss": "^4.0.0", "typescript": "^5.3.3", "unist-util-filter": "^5.0.1", "unist-util-visit": "^5.0.0", "zustand": "^4.3.2"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.10", "sharp": "0.33.1"}}