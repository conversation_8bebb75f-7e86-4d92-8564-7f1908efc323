export const metadata = {
  title: 'POST Account',
  description:
    'On this page, we’ll dive into the different account endpoints you can use to manage account programmatically.',
}

# Cuentas

Las cuentas son una parte esencial del core bancario. Este endpoint te permite **crear una nueva cuenta bancaria para un cliente existente** de forma programática, integrando automáticamente los datos requeridos por Temenos Transact.

## El modelo de cuenta

El modelo de cuenta define la estructura de datos necesaria para registrar una nueva cuenta en el core bancario. Este proceso implica proporcionar detalles sobre el cliente titular, el producto bancario asociado, y ciertas condiciones operativas y regulatorias.

---


# Endpoints disponibles de Cuentas/Accounts

## Account Create {{ tag: 'POST', label: '/account?CIF=' }}

<Row>
  <Col>

    Este endpoint crea una cuenta a un cliente existente.

    ### Required attributes

    <Properties>
      <Property name="CIF" type="string">
        ID del cliente dentro del core bancario
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/aacount?CIF=">

    ```bash {{ title: 'cURL' }}
      curl --location 'https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/account?CIF=100397' \
      --header 'T24-Token: {{T24-TOKEN}}' \
      --header 'x-api-key: xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80' \
      --header 'Content-Type: application/json' \
      --data '{
          "body": {
              "customerIds": [
                  {
                      "customerId": "100398"
                  }
              ],
              "properties": [
              {
              "propertyId": "CUSTOMER",
              "fields": [
                {
                  "fieldName": "CUSTOMER:1",
                  "fieldValue": "100397"
                },
                {
                  "fieldName": "CUSTOMER.ROLE:1",
                  "fieldValue": "OWNER"
                },
                {
                  "fieldName": "CUSTOMER:2",
                  "fieldValue": "112009"
                },
                {
                  "fieldName": "CUSTOMER.ROLE:2",
                  "fieldValue": "JOINT.OWNER"
                },

                {
                  "fieldName": "OTHER.PARTY:1",
                  "fieldValue": "100001"
                },
                {
                  "fieldName": "ROLE:1",
                  "fieldValue": "AGENTE"
                }


                ]
                },


                {
              "propertyId": "OFFICERS",
              "fields": [
                {
                  "fieldName": "PRIMARY.OFFICER",
                  "fieldValue": "1"
                },
                {
                  "fieldName": "OTHER.OFFICER:1",
                  "fieldValue": "26"
                },
                {
                  "fieldName": "OFFICER.ROLE:1",
                  "fieldValue": "ASESOR.FINANCIERO"
                }
                ]
                },


                {
              "propertyId": "BALANCE",
              "fields": [
                {
                  "fieldName": "ACCOUNT.TITLE.1",
                  "fieldValue": "Test Account Title"
                },
                {
                  "fieldName": "SHORT.TITLE",
                  "fieldValue": "Test Short Name"
                }
                ]
                }
                
              ],
              "activityId": "ACCOUNTS-NEW-ARRANGEMENT",
              "currencyId": "USD",
              "productId": "REG.CURRENT.ACCOUNT"
          }
      }'
    ```

    ```js
      const myHeaders = new Headers();
      myHeaders.append("T24-Token", "{{AWS-QA-TOKEN}}");
      myHeaders.append("x-api-key", "xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80");
      myHeaders.append("Content-Type", "application/json");

      const raw = JSON.stringify({
        "body": {
          "customerIds": [
            {
              "customerId": "100398"
            }
          ],
          "properties": [
            {
              "propertyId": "CUSTOMER",
              "fields": [
                {
                  "fieldName": "CUSTOMER:1",
                  "fieldValue": "100397"
                },
                {
                  "fieldName": "CUSTOMER.ROLE:1",
                  "fieldValue": "OWNER"
                },
                {
                  "fieldName": "CUSTOMER:2",
                  "fieldValue": "112009"
                },
                {
                  "fieldName": "CUSTOMER.ROLE:2",
                  "fieldValue": "JOINT.OWNER"
                },
                {
                  "fieldName": "OTHER.PARTY:1",
                  "fieldValue": "100001"
                },
                {
                  "fieldName": "ROLE:1",
                  "fieldValue": "AGENTE"
                }
              ]
            },
            {
              "propertyId": "OFFICERS",
              "fields": [
                {
                  "fieldName": "PRIMARY.OFFICER",
                  "fieldValue": "1"
                },
                {
                  "fieldName": "OTHER.OFFICER:1",
                  "fieldValue": "26"
                },
                {
                  "fieldName": "OFFICER.ROLE:1",
                  "fieldValue": "ASESOR.FINANCIERO"
                }
              ]
            },
            {
              "propertyId": "BALANCE",
              "fields": [
                {
                  "fieldName": "ACCOUNT.TITLE.1",
                  "fieldValue": "Test Account Title"
                },
                {
                  "fieldName": "SHORT.TITLE",
                  "fieldValue": "Test Short Name"
                }
              ]
            }
          ],
          "activityId": "ACCOUNTS-NEW-ARRANGEMENT",
          "currencyId": "USD",
          "productId": "REG.CURRENT.ACCOUNT"
        }
      });

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
        redirect: "follow"
      };

      fetch("https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/account?CIF=100397", requestOptions)
        .then((response) => response.text())
        .then((result) => console.log(result))
        .catch((error) => console.error(error));
    ```

    ```python
    import requests
    import json

    url = "https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/account?CIF=100397"

    payload = json.dumps({
      "body": {
        "customerIds": [
          {
            "customerId": "100398"
          }
        ],
        "properties": [
          {
            "propertyId": "CUSTOMER",
            "fields": [
              {
                "fieldName": "CUSTOMER:1",
                "fieldValue": "100397"
              },
              {
                "fieldName": "CUSTOMER.ROLE:1",
                "fieldValue": "OWNER"
              },
              {
                "fieldName": "CUSTOMER:2",
                "fieldValue": "112009"
              },
              {
                "fieldName": "CUSTOMER.ROLE:2",
                "fieldValue": "JOINT.OWNER"
              },
              {
                "fieldName": "OTHER.PARTY:1",
                "fieldValue": "100001"
              },
              {
                "fieldName": "ROLE:1",
                "fieldValue": "AGENTE"
              }
            ]
          },
          {
            "propertyId": "OFFICERS",
            "fields": [
              {
                "fieldName": "PRIMARY.OFFICER",
                "fieldValue": "1"
              },
              {
                "fieldName": "OTHER.OFFICER:1",
                "fieldValue": "26"
              },
              {
                "fieldName": "OFFICER.ROLE:1",
                "fieldValue": "ASESOR.FINANCIERO"
              }
            ]
          },
          {
            "propertyId": "BALANCE",
            "fields": [
              {
                "fieldName": "ACCOUNT.TITLE.1",
                "fieldValue": "Test Account Title"
              },
              {
                "fieldName": "SHORT.TITLE",
                "fieldValue": "Test Short Name"
              }
            ]
          }
        ],
        "activityId": "ACCOUNTS-NEW-ARRANGEMENT",
        "currencyId": "USD",
        "productId": "REG.CURRENT.ACCOUNT"
      }
    })
    headers = {
      'T24-Token': '{{AWS-QA-TOKEN}}',
      'x-api-key': 'xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80',
      'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)

    ```
    </CodeGroup>

  </Col>
</Row>

  ---
  # Seguridad y Configuración del Endpoint POST / Account

  Esta sección describe cómo se ha configurado el **API Gateway** que expone el endpoint de creación de cuentas, garantizando un manejo seguro, eficiente y controlado de las solicitudes entrantes.

  ---

  ## 🔐 Seguridad: CORS y Encabezados Personalizados

  ### ✅ Habilitación de CORS

  Para permitir el consumo del API desde aplicaciones web o herramientas externas, se habilita **CORS** en el método `POST`:

  - `Access-Control-Allow-Origin: BPM`  
    (limitado a nuestro entorno productivo de nuestro BPM).
  - `Access-Control-Allow-Methods: POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, Authorization, T24-Token, x-api-key`

  Esto permite que navegadores modernos interactúen con el API sin restricciones innecesarias, respetando los estándares web.

  ---

  ### 🧾 Encabezados Personalizados

  Para reforzar la seguridad y trazabilidad, se procesan y validan los siguientes encabezados:

  - `T24-Token`: Token requerido para autenticar contra el core bancario.
  - `x-api-key`: Clave de API administrada desde API Gateway.
  - `User-Agent`, `Accept`, `Host`: Información básica de cliente.
  - `X-Forwarded-For`, `X-Forwarded-Port`, `X-Forwarded-Proto`: Información de red para auditoría.
  - `X-Amzn-Trace-Id`: ID de trazabilidad generado por AWS.

  ---

  ## ⚙️ Plantilla de Integración (Mapping Template)

  Para proteger la función Lambda y controlar la entrada de datos, se usa una plantilla personalizada en formato **VTL (Velocity Template Language)**. Esta plantilla transforma la solicitud HTTP en un JSON estructurado, asegurando que solo se transmita la información permitida.

  ### 🧩 Plantilla de ejemplo – POST / Account

  ```json
  {
    "queryStringParameters": {
      "CIF": "$input.params().querystring.CIF"
    },
    "body": $input.body,
    "headers": {
      "Accept": "$input.params().header.Accept",
      "Accept-Encoding": "$input.params().header.Accept-Encoding",
      "Cache-Control": "$input.params().header.Cache-Control",
      "Host": "$input.params().header.Host",
      "User-Agent": "$input.params().header.User-Agent",
      "X-Amzn-Trace-Id": "$input.params().header.X-Amzn-Trace-Id",
      "x-api-key": "$input.params().header.x-api-key",
      "T24-Token": "$input.params().header.T24-Token",
      "X-Forwarded-For": "$context.identity.sourceIp",
      "X-Forwarded-Port": "$context.identity.sourcePort",
      "X-Forwarded-Proto": "$context.protocol"
    }
  }
  ```
---

  ## ✅ Respuesta Exitosa

```json {{ title: 'Response' }}
    {
      {
        "statusCode": 200,
        "accountId" : "*******"
      }
    }
    ```