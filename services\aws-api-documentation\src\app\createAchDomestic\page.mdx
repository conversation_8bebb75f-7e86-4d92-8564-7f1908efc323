export const metadata = {
  title: 'POST ach-domestic',
  description:
    'On this page, we’ll dive into the different ach-domestic endpoints you can use to manage ach-domestic programmatically.',
}

# Transferencias ACH Nacionales (Single)

Este endpoint permite **realizar transferencias ACH nacionales de tipo individual**, es decir, enviar dinero desde una cuenta de Prival hacia cuentas externas en otros bancos locales. Está diseñado para ejecutar pagos o transferencias únicas hacia terceros fuera del banco, incluyendo abonos a préstamos o cuentas bancarias en entidades financieras nacionales.

## ¿Qué es una transferencia ACH nacional?

Una transferencia ACH (Automated Clearing House) nacional es un método de pago electrónico que permite mover fondos desde una cuenta en Prival hacia cuentas en otros bancos del país.

---


# Endpoints disponibles de ach-domestic

## Customer Search {{ tag: 'POST', label: '/ach-domestic' }}

<Row>
  <Col>

    Este endpoint crea una transferencia ACH nacional.

    ### Required attributes

    <Properties>
      <Property name="Body" type="string">
        Todo el cuerpo de la solicitud es requerido
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/ach-domestic">

    ```bash {{ title: 'cURL' }}
      curl --location 'https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/ach-domestic' \
      --header 'x-api-key: xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80' \
      --header 'T24-Token: {{T24-TOKEN}}' \
      --header 'Content-Type: application/json' \
      --data '{
          "header": {},
          "body": {
              "currency": "USD",
              "companyDescDate": "",
              "companyName": "JHON SMITH",
              "companyDiscretionaryData": "08-00-00000-000000",
              "companyIdentification": "**********",
              "offsetAccount": "************",
              "payOption": "SINGLE.PAYMENT",
              "effectiveDate": "********",
              "secutiryCode":"Lat Ppd-preagreed Payment-deposit",
              "companyEntryDescription": ".",
              "customerId": "101220",
              "customerCurrency": "101220*USD",
              "beneficiary": "",
              "serviceClassCode": "220",
              "transactionCode": "32",
              "paymentFrequency": "",
              "standardEntryClassCode": "PPD",
              "routingNo": "BAGEPAPA",
              "dfiAccountNumber": "*************",
              "amount": "135.76",
              "individualIdNo": "",
              "receivingCoName": "JOSE GONZALEZ",
              "discretionaryData": "",
              "paymentInfo": "ACH",
              "CategPurpose":"Cash Transfer",
              "Purpose":"Cash Transfer"
          }
      }'
    ```

    ```js
    const myHeaders = new Headers();
      myHeaders.append("x-api-key", "xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80");
      myHeaders.append("T24-Token", "{{AWS-QA-TOKEN}}");
      myHeaders.append("Content-Type", "application/json");

      const raw = JSON.stringify({
        "header": {},
        "body": {
          "currency": "USD",
          "companyDescDate": "",
          "companyName": "JHON SMITH",
          "companyDiscretionaryData": "08-00-00800-000000",
          "companyIdentification": "**********",
          "offsetAccount": "************",
          "payOption": "SINGLE.PAYMENT",
          "effectiveDate": "********",
          "secutiryCode": "Lat Ppd-preagreed Payment-deposit",
          "companyEntryDescription": ".",
          "customerId": "101220",
          "customerCurrency": "101220*USD",
          "beneficiary": "",
          "serviceClassCode": "220",
          "transactionCode": "32",
          "paymentFrequency": "",
          "standardEntryClassCode": "PPD",
          "routingNo": "BAGEPAPA",
          "dfiAccountNumber": "*************",
          "amount": "135.76",
          "individualIdNo": "",
          "receivingCoName": "JOSE GONZALEZ",
          "discretionaryData": "",
          "paymentInfo": "ACH",
          "CategPurpose": "Cash Transfer",
          "Purpose": "Cash Transfer"
        }
      });

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
        redirect: "follow"
      };

      fetch("https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/ach-domestic", requestOptions)
        .then((response) => response.text())
        .then((result) => console.log(result))
        .catch((error) => console.error(error));
    ```

    ```python
        import requests
        import json

        url = "https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/ach-domestic"

        payload = json.dumps({
          "header": {},
          "body": {
            "currency": "USD",
            "companyDescDate": "",
            "companyName": "JHON SMITH",
            "companyDiscretionaryData": "08-00-00800-000000",
            "companyIdentification": "**********",
            "offsetAccount": "************",
            "payOption": "SINGLE.PAYMENT",
            "effectiveDate": "********",
            "secutiryCode": "Lat Ppd-preagreed Payment-deposit",
            "companyEntryDescription": ".",
            "customerId": "101220",
            "customerCurrency": "101220*USD",
            "beneficiary": "",
            "serviceClassCode": "220",
            "transactionCode": "32",
            "paymentFrequency": "",
            "standardEntryClassCode": "PPD",
            "routingNo": "BAGEPAPA",
            "dfiAccountNumber": "*************",
            "amount": "135.76",
            "individualIdNo": "",
            "receivingCoName": "JOSE GONZALEZ",
            "discretionaryData": "",
            "paymentInfo": "ACH",
            "CategPurpose": "Cash Transfer",
            "Purpose": "Cash Transfer"
          }
        })
        headers = {
          'x-api-key': 'xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80',
          'T24-Token': '{{AWS-QA-TOKEN}}',
          'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        print(response.text)

    ```
    </CodeGroup>

  </Col>
</Row>

  ---
  # Seguridad y Configuración del Endpoint POST /ach-domestic

  Esta sección describe cómo se ha configurado el **API Gateway** que expone el endpoint de ach-domestic, garantizando un manejo seguro, eficiente y controlado de las solicitudes entrantes.

  ---

  ## 🔐 Seguridad: CORS y Encabezados Personalizados

  ### ✅ Habilitación de CORS

  Para permitir el consumo del API desde aplicaciones web o herramientas externas, se habilita **CORS** en el método `POST`:

  - `Access-Control-Allow-Origin: BPM`  
    (limitado a nuestro entorno productivo de nuestro BPM).
  - `Access-Control-Allow-Methods: POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, Authorization, T24-Token, x-api-key`

  Esto permite que navegadores modernos interactúen con el API sin restricciones innecesarias, respetando los estándares web.

  ---

  ### 🧾 Encabezados Personalizados

  Para reforzar la seguridad y trazabilidad, se procesan y validan los siguientes encabezados:

  - `T24-Token`: Token requerido para autenticar contra el core bancario.
  - `x-api-key`: Clave de API administrada desde API Gateway.
  - `User-Agent`, `Accept`, `Host`: Información básica de cliente.
  - `X-Forwarded-For`, `X-Forwarded-Port`, `X-Forwarded-Proto`: Información de red para auditoría.
  - `X-Amzn-Trace-Id`: ID de trazabilidad generado por AWS.

  ---

  ## ⚙️ Plantilla de Integración (Mapping Template)

  Para proteger la función Lambda y controlar la entrada de datos, se usa una plantilla personalizada en formato **VTL (Velocity Template Language)**. Esta plantilla transforma la solicitud HTTP en un JSON estructurado, asegurando que solo se transmita la información permitida.

  ### 🧩 Plantilla de ejemplo – POST / funds-transfer

  ```json
  {
    "queryStringParameters": {
      #en este caso no es posible enviar query params
    },
    "body": $input.body,
    "headers": {
      "Accept": "$input.params().header.Accept",
      "Accept-Encoding": "$input.params().header.Accept-Encoding",
      "Cache-Control": "$input.params().header.Cache-Control",
      "Host": "$input.params().header.Host",
      "User-Agent": "$input.params().header.User-Agent",
      "X-Amzn-Trace-Id": "$input.params().header.X-Amzn-Trace-Id",
      "x-api-key": "$input.params().header.x-api-key",
      "T24-Token": "$input.params().header.T24-Token",
      "X-Forwarded-For": "$context.identity.sourceIp",
      "X-Forwarded-Port": "$context.identity.sourcePort",
      "X-Forwarded-Proto": "$context.protocol"
    }
  }
  ```
---

  ## ✅ Respuesta Exitosa

```json {{ title: 'Response' }}
    {
      {
        "statusCode": 200,
        "id": "*******",
        "ach-domestic": "*******"
      }
    }
    ```