export const metadata = {
  title: 'POST dpf',
  description:
    'On this page, we’ll dive into the different dpf endpoints you can use to manage dpf programmatically.',
}

# Depósitos a Plazo Fijo

Este endpoint permite **crear los depósitos a plazo fijo** de un cliente, tanto en su modalidad tradicional como indexada. Está diseñado para crear el producto de inversión gestionados por el core bancario, usando el identificador del cliente (CIF).

## ¿Qué es un depósito a plazo fijo?

Un depósito a plazo fijo es un producto financiero donde el cliente invierte una cantidad de dinero durante un tiempo determinado, con una tasa de interés predefinida (tradicional) o referenciada a un índice económico (indexado). Al vencimiento, el cliente recibe su capital más los intereses generados.

---


# Endpoints disponibles de DPF

## DPF Create {{ tag: 'POST', label: '/dpf' }}

<Row>
  <Col>

    Este endpoint crea un cliente en el core bancario.

    ### Required attributes

    <Properties>
      <Property name="Body" type="string">
        Todo el cuerpo de la solicitud es requerido
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/dpf">

    ```bash {{ title: 'cURL' }}
    curl --location 'https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/dpf' \
    --header 'x-api-key: xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80' \
    --header 'T24-Token: {{T24-TOKEN}}' \
    --header 'Content-Type: application/json' \
    --data '
    {
            "customerIds": [
                {
                    "customerId": "100001"
                }
            ],
            "properties": [
            {
                    "propertyId": "OFFICERS",
                    "fields": [
                        {
                            "fieldName": "PRIMARY.OFFICER",
                            "fieldValue": "1"
                        },
                        {
                            "fieldName": "OTHER.OFFICER:1",
                            "fieldValue": "26"
                        },
                        {
                            "fieldName": "OFFICER.ROLE:1",
                            "fieldValue": "ASESOR.FINANCIERO"
                        }
                    ]
                },
            {
            "propertyId": "ACCOUNT",
            "fields": [
              {
                "fieldName": "SHORT.TITLE",
                "fieldValue": "Jhon"
              }
              ]
              },
            {
            "propertyId": "COMMITMENT",
            "fields": [
              {
                "fieldName": "AMOUNT",
                "fieldValue": "10"
              },
              {
                "fieldName": "TERM",
                "fieldValue": "12M"
              },
              {
                "fieldName": "MATURITY.DATE",
                "fieldValue": "********"
              }
              ]
              },
              {
            "propertyId": "RENEWAL",
            "fields": [
              {
                "fieldName": "CHANGE.DATE.TYPE",
                "fieldValue": "PERIOD"
              },
              {
                "fieldName": "CHANGE.PERIOD",
                "fieldValue": "1M"
              },
              {
                "fieldName": "CHANGE.DATE",
                "fieldValue": "20260124"
              },
            {
                "fieldName": "CHANGE.ACTIVITY",
                "fieldValue": "DEPOSITS-ROLLOVER-ARRANGEMENT"
              },
              {
                "fieldName": "DEFAULT.ACTIVITY",
                "fieldValue": "DEPOSITS-ROLLOVER-ARRANGEMENT"
              },
            {
                "fieldName": "INITIATION.TYPE",
                "fieldValue": "AUTO"
              }
              ]
              },

              {
            "propertyId": "DEPOSITINT",
            "fields": [
              {
                "fieldName": "FIXED.RATE:1",
                "fieldValue": "1.5"
              },
              {
                "fieldName": "RATE.TIER.TYPE",
                "fieldValue": "LEVEL"
              }
              ]
              },

              {
            "propertyId": "SCHEDULE",
            "fields": [
              {
                "fieldName": "PAYMENT.TYPE",
                "fieldValue": "INTEREST"
              },
              {
                "fieldName": "PAYMENT.METHOD",
                "fieldValue": "PAY"
              },
              {
                "fieldName": "PROPERTY",
                "fieldValue": "DEPOSITINT"
              },
              {
                "fieldName": "PAYMENT.FREQ",
                "fieldValue": "e0Y e1M e0W e0D"
              },
              {
                "fieldName": "START.DATE",
                "fieldValue": ""
              },
              {
                "fieldName": "BILL.TYPE",
                "fieldValue": "PAYMENT"
              },
              {
                "fieldName": "ISSUE.BILL",
                "fieldValue": "NO"
              }
              ]
              },
              
              {
            "propertyId": "SETTLEMENT",
            "fields": [
              {
                "fieldName": "PAYMENT.TYPE",
                "fieldValue": "DEPOSIT.PRINCIPAL"
              },
              {
                "fieldName": "PAYIN.SETTLEMENT:1:1",
                "fieldValue": "YES"
              },
            {
                "fieldName": "PAYIN.ACCOUNT:1:1",
                "fieldValue": "************"     
              },
              {
                "fieldName": "PAYIN.AC.DB.RULE:1:1",
                "fieldValue": "FULL"     
              },
              {
                "fieldName": "PAYOUT.PPTY.CLASS:1:1",
                "fieldValue": "ACCOUNT"
              },
              {
                "fieldName": "PAYOUT.SETTLEMENT:1:1",
                "fieldValue": "YES"
              },
            {
                "fieldName": "PAYOUT.ACCOUNT:1:1",
                "fieldValue": "************"
              },
              {
                "fieldName": "PAYOUT.PPTY.CLASS:1:2",
                "fieldValue": "INTEREST"
              }
              ]
              }

            ],
            "activityId": "DEPOSITS-NEW-ARRANGEMENT",
            "currencyId": "USD",
            "productId": "FIXED.TERM.DEPOSIT"
        }'
    ```

    ```js
        const myHeaders = new Headers();
        myHeaders.append("x-api-key", "xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80");
        myHeaders.append("T24-Token", "{{T24-TOKEN}}");
        myHeaders.append("Content-Type", "application/json");

        const raw = JSON.stringify({
          "customerIds": [
            {
              "customerId": "100398"
            }
          ],
          "properties": [
            {
              "propertyId": "OFFICERS",
              "fields": [
                {
                  "fieldName": "PRIMARY.OFFICER",
                  "fieldValue": "1"
                },
                {
                  "fieldName": "OTHER.OFFICER:1",
                  "fieldValue": "26"
                },
                {
                  "fieldName": "OFFICER.ROLE:1",
                  "fieldValue": "ASESOR.FINANCIERO"
                }
              ]
            },
            {
              "propertyId": "ACCOUNT",
              "fields": [
                {
                  "fieldName": "SHORT.TITLE",
                  "fieldValue": "Pedro"
                }
              ]
            },
            {
              "propertyId": "COMMITMENT",
              "fields": [
                {
                  "fieldName": "AMOUNT",
                  "fieldValue": "10"
                },
                {
                  "fieldName": "TERM",
                  "fieldValue": "12M"
                },
                {
                  "fieldName": "MATURITY.DATE",
                  "fieldValue": "********"
                }
              ]
            },
            {
              "propertyId": "RENEWAL",
              "fields": [
                {
                  "fieldName": "CHANGE.DATE.TYPE",
                  "fieldValue": "PERIOD"
                },
                {
                  "fieldName": "CHANGE.PERIOD",
                  "fieldValue": "1M"
                },
                {
                  "fieldName": "CHANGE.DATE",
                  "fieldValue": "20260124"
                },
                {
                  "fieldName": "CHANGE.ACTIVITY",
                  "fieldValue": "DEPOSITS-ROLLOVER-ARRANGEMENT"
                },
                {
                  "fieldName": "DEFAULT.ACTIVITY",
                  "fieldValue": "DEPOSITS-ROLLOVER-ARRANGEMENT"
                },
                {
                  "fieldName": "INITIATION.TYPE",
                  "fieldValue": "AUTO"
                }
              ]
            },
            {
              "propertyId": "DEPOSITINT",
              "fields": [
                {
                  "fieldName": "FIXED.RATE:1",
                  "fieldValue": "1.5"
                },
                {
                  "fieldName": "RATE.TIER.TYPE",
                  "fieldValue": "LEVEL"
                }
              ]
            },
            {
              "propertyId": "SCHEDULE",
              "fields": [
                {
                  "fieldName": "PAYMENT.TYPE",
                  "fieldValue": "INTEREST"
                },
                {
                  "fieldName": "PAYMENT.METHOD",
                  "fieldValue": "PAY"
                },
                {
                  "fieldName": "PROPERTY",
                  "fieldValue": "DEPOSITINT"
                },
                {
                  "fieldName": "PAYMENT.FREQ",
                  "fieldValue": "e0Y e1M e0W e0D"
                },
                {
                  "fieldName": "START.DATE",
                  "fieldValue": ""
                },
                {
                  "fieldName": "BILL.TYPE",
                  "fieldValue": "PAYMENT"
                },
                {
                  "fieldName": "ISSUE.BILL",
                  "fieldValue": "NO"
                }
              ]
            },
            {
              "propertyId": "SETTLEMENT",
              "fields": [
                {
                  "fieldName": "PAYMENT.TYPE",
                  "fieldValue": "DEPOSIT.PRINCIPAL"
                },
                {
                  "fieldName": "PAYIN.SETTLEMENT:1:1",
                  "fieldValue": "YES"
                },
                {
                  "fieldName": "PAYIN.ACCOUNT:1:1",
                  "fieldValue": "************"
                },
                {
                  "fieldName": "PAYIN.AC.DB.RULE:1:1",
                  "fieldValue": "FULL"
                },
                {
                  "fieldName": "PAYOUT.PPTY.CLASS:1:1",
                  "fieldValue": "ACCOUNT"
                },
                {
                  "fieldName": "PAYOUT.SETTLEMENT:1:1",
                  "fieldValue": "YES"
                },
                {
                  "fieldName": "PAYOUT.ACCOUNT:1:1",
                  "fieldValue": "************"
                },
                {
                  "fieldName": "PAYOUT.PPTY.CLASS:1:2",
                  "fieldValue": "INTEREST"
                }
              ]
            }
          ],
          "activityId": "DEPOSITS-NEW-ARRANGEMENT",
          "currencyId": "USD",
          "productId": "FIXED.TERM.DEPOSIT"
        });

        const requestOptions = {
          method: "POST",
          headers: myHeaders,
          body: raw,
          redirect: "follow"
        };

        fetch("https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/dpf", requestOptions)
          .then((response) => response.text())
          .then((result) => console.log(result))
          .catch((error) => console.error(error));
    ```

    ```python
        import requests
        import json

        url = "https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/dpf"

        payload = json.dumps({
          "customerIds": [
            {
              "customerId": "100398"
            }
          ],
          "properties": [
            {
              "propertyId": "OFFICERS",
              "fields": [
                {
                  "fieldName": "PRIMARY.OFFICER",
                  "fieldValue": "1"
                },
                {
                  "fieldName": "OTHER.OFFICER:1",
                  "fieldValue": "26"
                },
                {
                  "fieldName": "OFFICER.ROLE:1",
                  "fieldValue": "ASESOR.FINANCIERO"
                }
              ]
            },
            {
              "propertyId": "ACCOUNT",
              "fields": [
                {
                  "fieldName": "SHORT.TITLE",
                  "fieldValue": "Pedro"
                }
              ]
            },
            {
              "propertyId": "COMMITMENT",
              "fields": [
                {
                  "fieldName": "AMOUNT",
                  "fieldValue": "10"
                },
                {
                  "fieldName": "TERM",
                  "fieldValue": "12M"
                },
                {
                  "fieldName": "MATURITY.DATE",
                  "fieldValue": "********"
                }
              ]
            },
            {
              "propertyId": "RENEWAL",
              "fields": [
                {
                  "fieldName": "CHANGE.DATE.TYPE",
                  "fieldValue": "PERIOD"
                },
                {
                  "fieldName": "CHANGE.PERIOD",
                  "fieldValue": "1M"
                },
                {
                  "fieldName": "CHANGE.DATE",
                  "fieldValue": "20260124"
                },
                {
                  "fieldName": "CHANGE.ACTIVITY",
                  "fieldValue": "DEPOSITS-ROLLOVER-ARRANGEMENT"
                },
                {
                  "fieldName": "DEFAULT.ACTIVITY",
                  "fieldValue": "DEPOSITS-ROLLOVER-ARRANGEMENT"
                },
                {
                  "fieldName": "INITIATION.TYPE",
                  "fieldValue": "AUTO"
                }
              ]
            },
            {
              "propertyId": "DEPOSITINT",
              "fields": [
                {
                  "fieldName": "FIXED.RATE:1",
                  "fieldValue": "1.5"
                },
                {
                  "fieldName": "RATE.TIER.TYPE",
                  "fieldValue": "LEVEL"
                }
              ]
            },
            {
              "propertyId": "SCHEDULE",
              "fields": [
                {
                  "fieldName": "PAYMENT.TYPE",
                  "fieldValue": "INTEREST"
                },
                {
                  "fieldName": "PAYMENT.METHOD",
                  "fieldValue": "PAY"
                },
                {
                  "fieldName": "PROPERTY",
                  "fieldValue": "DEPOSITINT"
                },
                {
                  "fieldName": "PAYMENT.FREQ",
                  "fieldValue": "e0Y e1M e0W e0D"
                },
                {
                  "fieldName": "START.DATE",
                  "fieldValue": ""
                },
                {
                  "fieldName": "BILL.TYPE",
                  "fieldValue": "PAYMENT"
                },
                {
                  "fieldName": "ISSUE.BILL",
                  "fieldValue": "NO"
                }
              ]
            },
            {
              "propertyId": "SETTLEMENT",
              "fields": [
                {
                  "fieldName": "PAYMENT.TYPE",
                  "fieldValue": "DEPOSIT.PRINCIPAL"
                },
                {
                  "fieldName": "PAYIN.SETTLEMENT:1:1",
                  "fieldValue": "YES"
                },
                {
                  "fieldName": "PAYIN.ACCOUNT:1:1",
                  "fieldValue": "************"
                },
                {
                  "fieldName": "PAYIN.AC.DB.RULE:1:1",
                  "fieldValue": "FULL"
                },
                {
                  "fieldName": "PAYOUT.PPTY.CLASS:1:1",
                  "fieldValue": "ACCOUNT"
                },
                {
                  "fieldName": "PAYOUT.SETTLEMENT:1:1",
                  "fieldValue": "YES"
                },
                {
                  "fieldName": "PAYOUT.ACCOUNT:1:1",
                  "fieldValue": "************"
                },
                {
                  "fieldName": "PAYOUT.PPTY.CLASS:1:2",
                  "fieldValue": "INTEREST"
                }
              ]
            }
          ],
          "activityId": "DEPOSITS-NEW-ARRANGEMENT",
          "currencyId": "USD",
          "productId": "FIXED.TERM.DEPOSIT"
        })
        headers = {
          'x-api-key': 'xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80',
          'T24-Token': '{{T24-TOKEN}}',
          'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        print(response.text)


    ```
    </CodeGroup>

  </Col>
</Row>

  ---
  # Seguridad y Configuración del Endpoint POST / dpf

  Esta sección describe cómo se ha configurado el **API Gateway** que expone el endpoint de dpf, garantizando un manejo seguro, eficiente y controlado de las solicitudes entrantes.

  ---

  ## 🔐 Seguridad: CORS y Encabezados Personalizados

  ### ✅ Habilitación de CORS

  Para permitir el consumo del API desde aplicaciones web o herramientas externas, se habilita **CORS** en el método `POST`:

  - `Access-Control-Allow-Origin: BPM`  
    (limitado a nuestro entorno productivo de nuestro BPM).
  - `Access-Control-Allow-Methods: POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, Authorization, T24-Token, x-api-key`

  Esto permite que navegadores modernos interactúen con el API sin restricciones innecesarias, respetando los estándares web.

  ---

  ### 🧾 Encabezados Personalizados

  Para reforzar la seguridad y trazabilidad, se procesan y validan los siguientes encabezados:

  - `T24-Token`: Token requerido para autenticar contra el core bancario.
  - `x-api-key`: Clave de API administrada desde API Gateway.
  - `User-Agent`, `Accept`, `Host`: Información básica de cliente.
  - `X-Forwarded-For`, `X-Forwarded-Port`, `X-Forwarded-Proto`: Información de red para auditoría.
  - `X-Amzn-Trace-Id`: ID de trazabilidad generado por AWS.

  ---

  ## ⚙️ Plantilla de Integración (Mapping Template)

  Para proteger la función Lambda y controlar la entrada de datos, se usa una plantilla personalizada en formato **VTL (Velocity Template Language)**. Esta plantilla transforma la solicitud HTTP en un JSON estructurado, asegurando que solo se transmita la información permitida.

  ### 🧩 Plantilla de ejemplo – POST / DPF

  ```json
  {
    "queryStringParameters": {
      #en este caso no es posible enviar query params
    },
    "body": $input.body,
    "headers": {
      "Accept": "$input.params().header.Accept",
      "Accept-Encoding": "$input.params().header.Accept-Encoding",
      "Cache-Control": "$input.params().header.Cache-Control",
      "Host": "$input.params().header.Host",
      "User-Agent": "$input.params().header.User-Agent",
      "X-Amzn-Trace-Id": "$input.params().header.X-Amzn-Trace-Id",
      "x-api-key": "$input.params().header.x-api-key",
      "T24-Token": "$input.params().header.T24-Token",
      "X-Forwarded-For": "$context.identity.sourceIp",
      "X-Forwarded-Port": "$context.identity.sourcePort",
      "X-Forwarded-Proto": "$context.protocol"
    }
  }
  ```
---

  ## ✅ Respuesta Exitosa

```json {{ title: 'Response' }}
    {
      {
        "statusCode": 200,
        "id": "*******",
        "dpf": "*******"
      }
    }
    ``