export const metadata = {
  title: 'POST dpf',
  description:
    'On this page, we’ll dive into the different dpf endpoints you can use to manage dpf programmatically.',
}

# Traspasos de Fondos

Este endpoint permite **realizar traspasos de fondos entre cuentas** dentro del banco, ya sea entre cuentas del mismo titular o entre distintos titulares, siempre que ambas cuentas pertenezcan a clientes del banco. Está diseñado para ejecutar operaciones de movimiento de dinero internas, gestionadas por el core bancario, utilizando los identificadores de las cuentas origen y destino.

## ¿Qué es un traspaso de fondos?

Un traspaso de fondos es una operación bancaria que permite mover dinero de una cuenta a otra.

---


# Endpoints disponibles de DPF

## Funds Transfer Create {{ tag: 'POST', label: '/funds-transfer' }}

<Row>
  <Col>

    Este endpoint crea una transferencia de fondos.

    ### Required attributes

    <Properties>
      <Property name="Body" type="string">
        Todo el cuerpo de la solicitud es requerido
      </Property>
    </Properties>

  </Col>
  <Col sticky>

    <CodeGroup title="Request" tag="POST" label="/funds-transfer">

    ```bash {{ title: 'cURL' }}
    curl --location 'https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/funds-transfer' \
    --header 'x-api-key: xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80' \
    --header 'T24-Token: {{T24-TOKEN}}' \
    --header 'Content-Type: application/json' \
    --data '{
      "body": {
        "debitAccount": "************",
        "debitCurrency": "USD",
        "debitAmount": "150.50",
        "creditAccount": "************",
        "creditCurrency": "USD",
        "creditAmount": "",
        "paymentDetail": "",
        "benOurCharges": "",
        "transactionType": "ACIB",
        "benCustomer": "",
        "benAcctNum": "",
        "beneficiary": "BEN2031800005",
        "bankSortCode": "",
        "ibanBen": "",
        "bicIbanBen": "",
        "payee": "prueba 15",
        "bicIbanBenName": "",
        "bicIbanBenCity": "",
        "override": ""
      }
    }'
    ```

    ```js
     const myHeaders = new Headers();
      myHeaders.append("x-api-key", "xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80");
      myHeaders.append("T24-Token", "{{T24-TOKEN}}");
      myHeaders.append("Content-Type", "application/json");

      const raw = JSON.stringify({
        "body": {
          "debitAccount": "************",
          "debitCurrency": "USD",
          "debitAmount": "150.50",
          "creditAccount": "************",
          "creditCurrency": "USD",
          "creditAmount": "",
          "paymentDetail": "",
          "benOurCharges": "",
          "transactionType": "ACIB",
          "benCustomer": "",
          "benAcctNum": "",
          "beneficiary": "BEN2031800005",
          "bankSortCode": "",
          "ibanBen": "",
          "bicIbanBen": "",
          "payee": "prueba 15",
          "bicIbanBenName": "",
          "bicIbanBenCity": "",
          "override": ""
        }
      });

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
        redirect: "follow"
      };

      fetch("https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/funds-transfer", requestOptions)
        .then((response) => response.text())
        .then((result) => console.log(result))
        .catch((error) => console.error(error));
    ```

    ```python
    import requests
    import json

    url = "https://ztr3w5aj41.execute-api.us-east-1.amazonaws.com/QA/funds-transfer"

    payload = json.dumps({
      "body": {
        "debitAccount": "************",
        "debitCurrency": "USD",
        "debitAmount": "150.50",
        "creditAccount": "************",
        "creditCurrency": "USD",
        "creditAmount": "",
        "paymentDetail": "",
        "benOurCharges": "",
        "transactionType": "ACIB",
        "benCustomer": "",
        "benAcctNum": "",
        "beneficiary": "BEN2031800005",
        "bankSortCode": "",
        "ibanBen": "",
        "bicIbanBen": "",
        "payee": "prueba 15",
        "bicIbanBenName": "",
        "bicIbanBenCity": "",
        "override": ""
      }
    })
    headers = {
      'x-api-key': 'xXlqWn4cPp9IQhkjVFkgbvJeauo9rN719dduKX80',
      'T24-Token': '{{AWS-QA-TOKEN}}',
      'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=payload)

    print(response.text)

    ```
    </CodeGroup>

  </Col>
</Row>

  ---
  # Seguridad y Configuración del Endpoint POST / funds-transfer

  Esta sección describe cómo se ha configurado el **API Gateway** que expone el endpoint de funds-transfer, garantizando un manejo seguro, eficiente y controlado de las solicitudes entrantes.

  ---

  ## 🔐 Seguridad: CORS y Encabezados Personalizados

  ### ✅ Habilitación de CORS

  Para permitir el consumo del API desde aplicaciones web o herramientas externas, se habilita **CORS** en el método `POST`:

  - `Access-Control-Allow-Origin: BPM`  
    (limitado a nuestro entorno productivo de nuestro BPM).
  - `Access-Control-Allow-Methods: POST, OPTIONS`
  - `Access-Control-Allow-Headers: Content-Type, Authorization, T24-Token, x-api-key`

  Esto permite que navegadores modernos interactúen con el API sin restricciones innecesarias, respetando los estándares web.

  ---

  ### 🧾 Encabezados Personalizados

  Para reforzar la seguridad y trazabilidad, se procesan y validan los siguientes encabezados:

  - `T24-Token`: Token requerido para autenticar contra el core bancario.
  - `x-api-key`: Clave de API administrada desde API Gateway.
  - `User-Agent`, `Accept`, `Host`: Información básica de cliente.
  - `X-Forwarded-For`, `X-Forwarded-Port`, `X-Forwarded-Proto`: Información de red para auditoría.
  - `X-Amzn-Trace-Id`: ID de trazabilidad generado por AWS.

  ---

  ## ⚙️ Plantilla de Integración (Mapping Template)

  Para proteger la función Lambda y controlar la entrada de datos, se usa una plantilla personalizada en formato **VTL (Velocity Template Language)**. Esta plantilla transforma la solicitud HTTP en un JSON estructurado, asegurando que solo se transmita la información permitida.

  ### 🧩 Plantilla de ejemplo – POST / funds-trasnfer

  ```json
  {
    "queryStringParameters": {
      #en este caso no es posible enviar query params
    },
    "body": $input.body,
    "headers": {
      "Accept": "$input.params().header.Accept",
      "Accept-Encoding": "$input.params().header.Accept-Encoding",
      "Cache-Control": "$input.params().header.Cache-Control",
      "Host": "$input.params().header.Host",
      "User-Agent": "$input.params().header.User-Agent",
      "X-Amzn-Trace-Id": "$input.params().header.X-Amzn-Trace-Id",
      "x-api-key": "$input.params().header.x-api-key",
      "T24-Token": "$input.params().header.T24-Token",
      "X-Forwarded-For": "$context.identity.sourceIp",
      "X-Forwarded-Port": "$context.identity.sourcePort",
      "X-Forwarded-Proto": "$context.protocol"
    }
  }
  ```
---

  ## ✅ Respuesta Exitosa

```json {{ title: 'Response' }}
    {
      {
        "statusCode": 200,
        "id": "*******",
        "dpf": "*******"
      }
    }
    ```