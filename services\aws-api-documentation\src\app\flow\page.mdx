export const metadata = {
  title: 'Flow',
  description:
    '',
}

# Flujo de Trabajo en Integradoc  

## **1 Inicio de Sesión y Autenticación**  
1. Un usuario accede a **Integradoc** desde su oficina o de manera remota.  
2. El sistema verifica su identidad con **Keycloak** y genera un **token de acceso seguro (JWT)**.  
3. Con este token, el usuario puede interactuar con las diferentes funciones del sistema.  

---

## **2 Solicitud de Información o Procesos Bancarios**  
1. El usuario elige una opción en la aplicación web (ej. consulta de cuenta, transferencia, etc.).  
2. Integradoc verifica el permiso del usuario y envía la solicitud al servicio correspondiente.  
3. Si la información está disponible en Integradoc, se recupera directamente de la base de datos.  
4. Si se necesita información de **Temenos (Core Bancario)**, la solicitud se envía a través de la conexión segura.  

---

## **3 Comunicación con Temenos (Core Bancario)**  
1. **Integradoc envía la solicitud** a AWS, donde se procesa en la **API Gateway**.  
2. Una **AWS Lambda** toma la solicitud y la reenvía a **Temenos** a través de una conexión **VPN segura**.  
3. **Temenos Transact** recibe la solicitud, busca la información o ejecuta la acción requerida.  
4. La respuesta de Temenos es enviada de vuelta a Integradoc por el mismo camino.  

---

## **4 Respuesta al Usuario**  
1. Integradoc recibe la respuesta y la formatea para mostrarla de manera clara.  
2. La información se envía de vuelta a la aplicación web.  
3. El usuario ve el resultado en pantalla en tiempo real.  
4. Todas las transacciones quedan registradas en los sistemas de monitoreo (**CloudWatch & CloudTrail**).  

---

#  Medidas de Seguridad  
**Conexión Segura:** Toda la comunicación con Temenos se realiza a través de **VPN Fortinet**.  
**Autenticación Robusta:** Se usan **tokens JWT con cifrado RS256** para proteger el acceso.  
**Protección Contra Ataques:** AWS cuenta con **Shield** para mitigar ataques DDoS.  
**Auditoría y Monitoreo:** CloudWatch y CloudTrail registran todas las interacciones para revisión y control.  

---

#  Ejemplo Práctico  
 **Situación:** Un ejecutivo bancario necesita verificar el saldo de una cuenta.  

**Paso 1:** Inicia sesión en Integradoc y selecciona “Consultar Saldo”.  
**Paso 2:** Integradoc verifica sus permisos y envía la solicitud.  
**Paso 3:** Si la información no está en la base de datos interna, la solicitud se envía a **Temenos Transact**.  
**Paso 4:** Temenos devuelve la información y se muestra el saldo en pantalla.  

 **Todo el proceso ocurre en segundos y de manera segura.**  


Esta arquitectura permite una integración segura y eficiente entre **Integradoc, los usuarios internos y Temenos Cloud**. La VPN garantiza la conectividad privada, mientras que AWS gestiona la escalabilidad y seguridad con **API Gateway, Lambda Functions y AWS Shield**.
