import { Guides } from '@/components/Guides'
import { Resources } from '@/components/Resources'
import { HeroPattern } from '@/components/HeroPattern'

export const metadata = {
  title: 'API Documentacion',
  description:
    'Learn everything there is to know about Prival APIs',
}

export const sections = [
  { title: 'Guías', id: 'guides' },
  { title: 'Recursos', id: 'resources' },
]

<HeroPattern />

# PBPA API DOCS

Esta documentación describe la arquitectura y el uso de las APIs desplegadas en API Gateway con funciones AWS Lambda que actúan como middleware para la integración con el core bancario. {{ className: 'lead' }}

<div className="not-prose mt-6 mb-16 flex gap-3">
  <Button href="/architecture" arrow="right">
    <>Arquitectura</>
  </Button>
</div>


<Guides />

<Resources />
