import { Libraries } from '@/components/Libraries'

export const metadata = {
  title: 'Protocol SDKs',
  description:
    'Protocol offers fine-tuned JavaScript, Ruby, PHP, Python, and Go libraries to make your life easier and give you the best experience when consuming the API.',
}

export const sections = [
  { title: 'Official libraries', id: 'official-libraries' },
]

# Protocol SDKs

The recommended way to interact with the Protocol API is by using one of our official SDKs. Today, Protocol offers fine-tuned JavaScript, Ruby, PHP, Python, and Go libraries to make your life easier and give you the best experience when consuming the API. {{ className: 'lead' }}

<Libraries />
