import { Button } from '@/components/Button'
import { Heading } from '@/components/Heading'

const guides = [
  {
    href: '/architecture',
    name: 'Arquitectura General',
    description: 'Descubre la estructura y componentes clave de Prival APIs.',
  },
  {
    href: '/flow',
    name: '<PERSON><PERSON><PERSON> de Trabajo',
    description: 'Explora un caso de uso práctico y el proceso de integración.',
  },
  {
    href: '/errors',
    name: '<PERSON><PERSON><PERSON> de Errores',
    description: 'Consulta los tipos de errores que puede devolver la API y sus soluciones.',
  },
];


export function Guides() {
  return (
    <div className="my-16 xl:max-w-none">
      <Heading level={2} id="guides">
        Guides
      </Heading>
      <div className="not-prose mt-4 grid grid-cols-1 gap-8 border-t border-zinc-900/5 pt-10 sm:grid-cols-2 xl:grid-cols-4 dark:border-white/5">
        {guides.map((guide) => (
          <div key={guide.href}>
            <h3 className="text-sm font-semibold text-zinc-900 dark:text-white">
              {guide.name}
            </h3>
            <p className="mt-1 text-sm text-zinc-600 dark:text-zinc-400">
              {guide.description}
            </p>
            <p className="mt-4">
              <Button href={guide.href} variant="text" arrow="right">
                Ir a documentación
              </Button>
            </p>
          </div>
        ))}
      </div>
    </div>
  )
}
