"""
Shared API module for the documentation system.
This module provides a Flask API that can be used by all microservices.
"""
from flask import Flask
from flask_cors import CORS
from .config import Config

def create_app(config_class=Config):
    """
    Factory function to create and configure the Flask application.
    
    Args:
        config_class: Configuration class to use
        
    Returns:
        Flask application instance
    """
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Enable CORS
    CORS(app)
    
    # Register blueprints
    from .routes.formularios import bp as formularios_bp
    from .routes.auth import bp as auth_bp
    from .routes.health import bp as health_bp
    
    app.register_blueprint(formularios_bp, url_prefix='/api/formularios')
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(health_bp, url_prefix='/api/health')
    
    # Register error handlers
    from .error_handlers import register_error_handlers
    register_error_handlers(app)
    
    return app