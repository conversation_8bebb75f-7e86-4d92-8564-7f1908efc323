"""
Authentication routes for the Flask API.
"""
from flask import Blueprint, request, jsonify
import uuid
import logging

bp = Blueprint('auth', __name__)
logger = logging.getLogger(__name__)

# Simple in-memory user store for demonstration
# In production, use a database
USERS = {
    'admin': {
        'password': 'admin123',
        'role': 'admin'
    },
    'user': {
        'password': 'user123',
        'role': 'user'
    }
}

# Active sessions
SESSIONS = {}

@bp.route('/login', methods=['POST'])
def login():
    """
    Login endpoint.
    
    Request body:
    {
        "username": "string",
        "password": "string"
    }
    
    Returns:
    {
        "status": "success",
        "user": "username",
        "session_id": "uuid",
        "role": "role"
    }
    """
    data = request.json
    
    if not data or 'username' not in data or 'password' not in data:
        return jsonify({
            'status': 'error',
            'message': 'Username and password are required'
        }), 400
    
    username = data['username']
    password = data['password']
    
    # Check if user exists and password is correct
    if username not in USERS or USERS[username]['password'] != password:
        logger.warning(f"Failed login attempt for user: {username}")
        return jsonify({
            'status': 'error',
            'message': 'Invalid username or password'
        }), 401
    
    # Create session
    session_id = str(uuid.uuid4())
    SESSIONS[session_id] = {
        'user': username,
        'role': USERS[username]['role'],
        'created_at': str(uuid.uuid1())
    }
    
    logger.info(f"User logged in: {username}")
    
    return jsonify({
        'status': 'success',
        'user': username,
        'session_id': session_id,
        'role': USERS[username]['role']
    })

@bp.route('/logout', methods=['POST'])
def logout():
    """
    Logout endpoint.
    
    Request body:
    {
        "session_id": "uuid"
    }
    
    Returns:
    {
        "status": "success",
        "message": "Logged out successfully"
    }
    """
    data = request.json
    
    if not data or 'session_id' not in data:
        return jsonify({
            'status': 'error',
            'message': 'Session ID is required'
        }), 400
    
    session_id = data['session_id']
    
    # Check if session exists
    if session_id in SESSIONS:
        username = SESSIONS[session_id]['user']
        del SESSIONS[session_id]
        logger.info(f"User logged out: {username}")
    
    return jsonify({
        'status': 'success',
        'message': 'Logged out successfully'
    })

@bp.route('/session', methods=['GET'])
def validate_session():
    """
    Validate session endpoint.
    
    Query parameters:
    - session_id: UUID of the session
    
    Returns:
    {
        "status": "success",
        "valid": true|false,
        "user": "username" (if valid),
        "role": "role" (if valid)
    }
    """
    session_id = request.args.get('session_id')
    
    if not session_id:
        return jsonify({
            'status': 'error',
            'message': 'Session ID is required'
        }), 400
    
    # Check if session exists
    if session_id in SESSIONS:
        return jsonify({
            'status': 'success',
            'valid': True,
            'user': SESSIONS[session_id]['user'],
            'role': SESSIONS[session_id]['role']
        })
    
    return jsonify({
        'status': 'success',
        'valid': False
    })