"""
Formularios routes for the Flask API.
"""
from flask import Blueprint, request, jsonify
import sys
import os
import logging
import json
from datetime import datetime

# Add the shared directory to the Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

from shared.db.database import SessionLocal
from shared.db.model import FormularioReporte

bp = Blueprint('formularios', __name__)
logger = logging.getLogger(__name__)

@bp.route('/', methods=['GET'])
def get_formularios():
    """
    Get all formularios.
    
    Returns:
    {
        "status": "success",
        "formularios": [
            {
                "id": "uuid",
                "name_reporte": "string",
                "description_reporte": "string",
                ...
            }
        ]
    }
    """
    try:
        db = SessionLocal()
        formularios = db.query(FormularioReporte).all()
        
        result = []
        for formulario in formularios:
            result.append({
                'id': str(formulario.id),
                'name_reporte': formulario.name_reporte,
                'description_reporte': formulario.description_reporte,
                'frequency_reporte': formulario.frequency_reporte,
                'path_reporte': formulario.path_reporte,
                'name_panel_reporte': formulario.name_panel_reporte,
                'name_analisis_reporte': formulario.name_analisis_reporte,
                'num_tabs_reporte': formulario.num_tabs_reporte
            })
        
        return jsonify({
            'status': 'success',
            'formularios': result
        })
    except Exception as e:
        logger.exception("Error getting formularios")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500
    finally:
        db.close()

@bp.route('/<formulario_id>', methods=['GET'])
def get_formulario(formulario_id):
    """
    Get a specific formulario.
    
    Path parameters:
    - formulario_id: UUID of the formulario
    
    Returns:
    {
        "status": "success",
        "formulario": {
            "id": "uuid",
            "name_reporte": "string",
            "description_reporte": "string",
            ...
        }
    }
    """
    try:
        db = SessionLocal()
        formulario = db.query(FormularioReporte).filter(FormularioReporte.id == formulario_id).first()
        
        if not formulario:
            return jsonify({
                'status': 'error',
                'message': 'Formulario not found'
            }), 404
        
        result = {
            'id': str(formulario.id),
            'method': formulario.method,
            'operation': formulario.operation,
            'version': formulario.version,
            'autor': formulario.autor,
            'name_reporte': formulario.name_reporte,
            'description_reporte': formulario.description_reporte,
            'frequency_reporte': formulario.frequency_reporte,
            'path_reporte': formulario.path_reporte,
            'name_panel_reporte': formulario.name_panel_reporte,
            'name_analisis_reporte': formulario.name_analisis_reporte,
            'num_tabs_reporte': formulario.num_tabs_reporte,
            'departments_report': formulario.departments_report,
            'cdatos_reporte': formulario.cdatos_reporte,
            'filter_reporte': formulario.filter_reporte,
            'calculated_reporte': formulario.calculated_reporte
        }
        
        return jsonify({
            'status': 'success',
            'formulario': result
        })
    except Exception as e:
        logger.exception(f"Error getting formulario {formulario_id}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500
    finally:
        db.close()

@bp.route('/', methods=['POST'])
def create_formulario():
    """
    Create a new formulario.
    
    Request body: JSON object with formulario data
    
    Returns:
    {
        "status": "success",
        "message": "Formulario created successfully",
        "id": "uuid"
    }
    """
    try:
        data = request.json
        
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No data provided'
            }), 400
        
        # Extract basic info
        method = data.get('methods', '')
        operation = data.get('operation', '')
        version = data.get('version', '1.0')
        autor = data.get('autor', '')
        
        # Extract info_reporte
        info_reporte = data.get('info_reporte', {})
        name_reporte = info_reporte.get('name_reporte', '')
        description_reporte = info_reporte.get('description_reporte', '')
        frequency_reporte = info_reporte.get('frequency_reporte', '')
        path_reporte = info_reporte.get('path_reporte', '')
        
        # Extract info_tecnic_reporte
        info_tecnic_reporte = data.get('info_tecnic_reporte', {})
        name_panel_reporte = info_tecnic_reporte.get('name_Panel_reporte', '')
        name_analisis_reporte = info_tecnic_reporte.get('name_analisis_reporte', '')
        num_tabs_reporte = info_tecnic_reporte.get('num_tabs_reporte', 0)
        departments_report = info_tecnic_reporte.get('department_report', [])
        
        # Extract other sections
        cdatos_reporte = data.get('cdatos_reporte', [])
        filter_reporte = data.get('filter_reporte', [])
        calculated_reporte = data.get('calculated_reporte', [])
        
        # Create new formulario
        formulario = FormularioReporte(
            method=method,
            operation=operation,
            version=version,
            autor=autor,
            name_reporte=name_reporte,
            description_reporte=description_reporte,
            frequency_reporte=frequency_reporte,
            path_reporte=path_reporte,
            name_panel_reporte=name_panel_reporte,
            name_analisis_reporte=name_analisis_reporte,
            num_tabs_reporte=num_tabs_reporte,
            departments_report=departments_report,
            cdatos_reporte=cdatos_reporte,
            filter_reporte=filter_reporte,
            calculated_reporte=calculated_reporte
        )
        
        db = SessionLocal()
        db.add(formulario)
        db.commit()
        db.refresh(formulario)
        
        logger.info(f"Formulario created: {formulario.id}")
        
        return jsonify({
            'status': 'success',
            'message': 'Formulario created successfully',
            'id': str(formulario.id)
        })
    except Exception as e:
        logger.exception("Error creating formulario")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500
    finally:
        db.close()

@bp.route('/<formulario_id>', methods=['PUT'])
def update_formulario(formulario_id):
    """
    Update an existing formulario.
    
    Path parameters:
    - formulario_id: UUID of the formulario
    
    Request body: JSON object with formulario data
    
    Returns:
    {
        "status": "success",
        "message": "Formulario updated successfully"
    }
    """
    try:
        data = request.json
        
        if not data:
            return jsonify({
                'status': 'error',
                'message': 'No data provided'
            }), 400
        
        db = SessionLocal()
        formulario = db.query(FormularioReporte).filter(FormularioReporte.id == formulario_id).first()
        
        if not formulario:
            return jsonify({
                'status': 'error',
                'message': 'Formulario not found'
            }), 404
        
        # Update basic info
        if 'methods' in data:
            formulario.method = data['methods']
        if 'operation' in data:
            formulario.operation = data['operation']
        if 'version' in data:
            formulario.version = data['version']
        if 'autor' in data:
            formulario.autor = data['autor']
        
        # Update info_reporte
        info_reporte = data.get('info_reporte', {})
        if 'name_reporte' in info_reporte:
            formulario.name_reporte = info_reporte['name_reporte']
        if 'description_reporte' in info_reporte:
            formulario.description_reporte = info_reporte['description_reporte']
        if 'frequency_reporte' in info_reporte:
            formulario.frequency_reporte = info_reporte['frequency_reporte']
        if 'path_reporte' in info_reporte:
            formulario.path_reporte = info_reporte['path_reporte']
        
        # Update info_tecnic_reporte
        info_tecnic_reporte = data.get('info_tecnic_reporte', {})
        if 'name_Panel_reporte' in info_tecnic_reporte:
            formulario.name_panel_reporte = info_tecnic_reporte['name_Panel_reporte']
        if 'name_analisis_reporte' in info_tecnic_reporte:
            formulario.name_anal