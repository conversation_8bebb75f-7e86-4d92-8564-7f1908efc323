"""
Health check routes for the Flask API.
"""
from flask import Blueprint, jsonify
import sys
import platform
from datetime import datetime

bp = Blueprint('health', __name__)

@bp.route('/ping', methods=['GET'])
def ping():
    """Simple health check endpoint."""
    return jsonify({'status': 'ok', 'message': 'pong'})

@bp.route('/status', methods=['GET'])
def status():
    """Detailed health check endpoint."""
    return jsonify({
        'status': 'ok',
        'timestamp': datetime.utcnow().isoformat(),
        'system': {
            'python_version': sys.version,
            'platform': platform.platform()
        }
    })