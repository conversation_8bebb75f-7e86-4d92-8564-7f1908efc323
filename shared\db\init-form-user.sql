-- Note: Database 'formularios' is already created by <PERSON><PERSON> Compose
-- This script runs in the context of that database

-- Create the form_user if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'form_user') THEN
        CREATE USER form_user WITH PASSWORD 'una_contraseña_segura' LOGIN;
        RAISE NOTICE 'Created user form_user with login privileges';
    ELSE
        RAISE NOTICE 'User form_user already exists';
    END IF;
END
$$;

-- Grant connection privileges to the current database
GRANT CONNECT ON DATABASE formularios TO form_user;
GRANT USAGE, CREATE ON SCHEMA public TO form_user;

-- Grant table permissions (for existing and future tables)
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO form_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO form_user;

-- Set default privileges for future tables and sequences
ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT SELECT, INSERT, UPDATE ON TABLES TO form_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT USAGE, SELECT ON SEQUENCES TO form_user;

-- Ensure form_user can login and has correct attributes
ALTER ROLE form_user LOGIN NOSUPERUSER NOCREATEDB NOCREATEROLE INHERIT;

-- Verify the user was created correctly
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'form_user' AND rolcanlogin = true) THEN
        RAISE NOTICE 'form_user verified: can login = true';
    ELSE
        RAISE WARNING 'form_user verification failed: login may be disabled';
    END IF;
END
$$;

-- Simple verification using basic queries that work during initialization
-- Check that the user was created successfully
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'form_user' AND rolcanlogin = true) THEN
        RAISE NOTICE 'form_user verification: user exists and can login';
    ELSE
        RAISE WARNING 'form_user verification: user not found or cannot login';
    END IF;
END
$$;

-- Log successful completion of permission grants
\echo 'CREATE privileges granted to form_user on public schema'
\echo 'Table and sequence permissions granted to form_user'
\echo 'Default privileges configured for future objects'

-- Log the user creation
\echo 'Form user setup completed successfully'
