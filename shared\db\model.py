import uuid
from sqlalchemy import Column, String, Text, Integer
from sqlalchemy.dialects.postgresql import UUI<PERSON>, JSONB
from .database import Base

class FormularioReporte(Base):
    __tablename__ = "formularios_reporte"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    method = Column(String, nullable=True)
    operation = Column(String, nullable=True)
    version = Column(String)
    autor = Column(String)

    name_reporte = Column(String)
    description_reporte = Column(Text)
    frequency_reporte = Column(String)
    path_reporte = Column(String)

    name_panel_reporte = Column(String)
    name_analisis_reporte = Column(String)
    num_tabs_reporte = Column(Integer)

    departments_report = Column(JSONB)
    cdatos_reporte = Column(JSONB)
    filter_reporte = Column(JSONB)
    calculated_reporte = Column(JSONB)