-- Test script to verify the database initialization works correctly
-- This script can be run manually to test the init-form-user.sql script

-- Connect to the formularios database
\c formularios;

-- Show current database
SELECT current_database() as current_db;

-- Check if form_user exists
\echo 'Checking if form_user exists...'
SELECT rolname, rolcanlogin, rolsuper, rolcreatedb, rolcreaterole, rolinherit
FROM pg_roles 
WHERE rolname = 'form_user';

-- Test if form_user can connect (this would be done from outside)
\echo 'form_user should be able to connect with: psql -h postgres_db -U form_user -d formularios'

-- Check what schemas exist
\echo 'Available schemas:'
SELECT schema_name FROM information_schema.schemata ORDER BY schema_name;

-- Check if public schema exists
\echo 'Checking public schema...'
SELECT nspname as schema_name, nspowner 
FROM pg_namespace 
WHERE nspname = 'public';

-- Test CREATE privilege by attempting to create a test table as form_user
-- Note: This would need to be run as form_user to truly test
\echo 'To test CREATE privilege, run as form_user:'
\echo 'CREATE TABLE test_permissions (id SERIAL PRIMARY KEY, test_col TEXT);'
\echo 'DROP TABLE test_permissions;'

-- Show all tables in public schema
\echo 'Current tables in public schema:'
SELECT tablename FROM pg_tables WHERE schemaname = 'public';

-- Show completion message
\echo 'Database initialization test completed'
\echo 'If no errors appeared above, the initialization should work correctly'
