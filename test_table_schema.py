#!/usr/bin/env python3
"""
Test script to verify the table schema can handle the sample JSON data structure.
This script tests table creation and data insertion with the provided sample data.
"""

import sys
import json
from pathlib import Path

# Sample JSON data from the user
SAMPLE_JSON_DATA = {
    "method": "post",
    "operation": "New Reporte", 
    "version": "1.0",
    "autor": "xxxx",
    "info_reporte": {
        "name_reporte": "Reporte prueba",
        "description_reporte": "uyfigjfuy", 
        "frequency_reporte": "Anual",
        "path_reporte": "Local"
    },
    "info_tecnic_reporte": {
        "name_Panel_reporte": "",
        "name_analisis_reporte": "Prueba analisis",
        "num_tabs_reporte": 1,
        "department_report": ["Finanzas", "Mercadeo"]
    },
    "cdatos_reporte": [
        {
            "cd_name_reporte": "bd_prueba",
            "cd_tipo_reporte": "Directa", 
            "cd_table_reporte": ""
        }
    ],
    "characteristics_reporte": {
        "filter_reporte": [
            {
                "nombre_del_filtro": "",
                "columna": "",
                "valor_predeterminado": ""
            }
        ],
        "calculated_reporte": [
            {
                "nombre_del_campo_calculado": "",
                "código_del_campo_calculado": ""
            }
        ]
    }
}

def test_table_creation():
    """Test that tables can be created with proper permissions"""
    print("🔧 Testing table creation...")
    
    try:
        # Add shared directory to Python path
        shared_path = Path(__file__).parent / "shared"
        sys.path.insert(0, str(shared_path))
        
        from db.database import engine, Base, test_connection
        from db.model import FormularioReporte
        
        # Test database connection first
        success, message = test_connection()
        if not success:
            print(f"❌ Database connection failed: {message}")
            return False
        
        print(f"✅ Database connection: {message}")
        
        # Try to create tables
        Base.metadata.create_all(bind=engine)
        print("✅ Tables created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        return False

def test_data_insertion():
    """Test that the sample JSON data can be inserted into the table"""
    print("\n📝 Testing data insertion...")
    
    try:
        # Add shared directory to Python path
        shared_path = Path(__file__).parent / "shared"
        sys.path.insert(0, str(shared_path))
        
        from db.database import SessionLocal
        from db.model import FormularioReporte
        
        db = SessionLocal()
        
        try:
            # Create a new form record using the sample data
            nuevo_formulario = FormularioReporte(
                method=SAMPLE_JSON_DATA.get("method"),
                operation=SAMPLE_JSON_DATA.get("operation"),
                version=SAMPLE_JSON_DATA.get("version"),
                autor=SAMPLE_JSON_DATA.get("autor"),

                name_reporte=SAMPLE_JSON_DATA.get("info_reporte", {}).get("name_reporte"),
                description_reporte=SAMPLE_JSON_DATA.get("info_reporte", {}).get("description_reporte"),
                frequency_reporte=SAMPLE_JSON_DATA.get("info_reporte", {}).get("frequency_reporte"),
                path_reporte=SAMPLE_JSON_DATA.get("info_reporte", {}).get("path_reporte"),

                name_panel_reporte=SAMPLE_JSON_DATA.get("info_tecnic_reporte", {}).get("name_Panel_reporte"),
                name_analisis_reporte=SAMPLE_JSON_DATA.get("info_tecnic_reporte", {}).get("name_analisis_reporte"),
                num_tabs_reporte=SAMPLE_JSON_DATA.get("info_tecnic_reporte", {}).get("num_tabs_reporte"),

                departments_report=SAMPLE_JSON_DATA.get("info_tecnic_reporte", {}).get("department_report", []),
                cdatos_reporte=SAMPLE_JSON_DATA.get("cdatos_reporte", []),
                filter_reporte=SAMPLE_JSON_DATA.get("characteristics_reporte", {}).get("filter_reporte", []),
                calculated_reporte=SAMPLE_JSON_DATA.get("characteristics_reporte", {}).get("calculated_reporte", [])
            )
            
            db.add(nuevo_formulario)
            db.commit()
            
            print(f"✅ Data inserted successfully with ID: {nuevo_formulario.id}")
            
            # Verify the data was stored correctly
            stored_record = db.query(FormularioReporte).filter_by(id=nuevo_formulario.id).first()
            
            if stored_record:
                print("✅ Data verification successful:")
                print(f"  - Name: {stored_record.name_reporte}")
                print(f"  - Description: {stored_record.description_reporte}")
                print(f"  - Departments: {stored_record.departments_report}")
                print(f"  - Data sources: {len(stored_record.cdatos_reporte)} items")
                print(f"  - Filters: {len(stored_record.filter_reporte)} items")
                print(f"  - Calculated fields: {len(stored_record.calculated_reporte)} items")
            else:
                print("❌ Data verification failed: record not found")
                return False
            
            return True
            
        except Exception as e:
            db.rollback()
            print(f"❌ Data insertion failed: {e}")
            return False
        finally:
            db.close()
            
    except ImportError as e:
        print(f"⚠️ Import error (expected outside Docker): {e}")
        return True
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_json_structure_compatibility():
    """Test that the JSON structure is compatible with the model"""
    print("\n🔍 Testing JSON structure compatibility...")
    
    # Check that all required fields are present
    required_mappings = {
        "method": SAMPLE_JSON_DATA.get("method"),
        "operation": SAMPLE_JSON_DATA.get("operation"),
        "version": SAMPLE_JSON_DATA.get("version"),
        "autor": SAMPLE_JSON_DATA.get("autor"),
        "name_reporte": SAMPLE_JSON_DATA.get("info_reporte", {}).get("name_reporte"),
        "description_reporte": SAMPLE_JSON_DATA.get("info_reporte", {}).get("description_reporte"),
        "frequency_reporte": SAMPLE_JSON_DATA.get("info_reporte", {}).get("frequency_reporte"),
        "path_reporte": SAMPLE_JSON_DATA.get("info_reporte", {}).get("path_reporte"),
        "name_panel_reporte": SAMPLE_JSON_DATA.get("info_tecnic_reporte", {}).get("name_Panel_reporte"),
        "name_analisis_reporte": SAMPLE_JSON_DATA.get("info_tecnic_reporte", {}).get("name_analisis_reporte"),
        "num_tabs_reporte": SAMPLE_JSON_DATA.get("info_tecnic_reporte", {}).get("num_tabs_reporte"),
        "departments_report": SAMPLE_JSON_DATA.get("info_tecnic_reporte", {}).get("department_report", []),
        "cdatos_reporte": SAMPLE_JSON_DATA.get("cdatos_reporte", []),
        "filter_reporte": SAMPLE_JSON_DATA.get("characteristics_reporte", {}).get("filter_reporte", []),
        "calculated_reporte": SAMPLE_JSON_DATA.get("characteristics_reporte", {}).get("calculated_reporte", [])
    }
    
    print("✅ Field mapping verification:")
    for field, value in required_mappings.items():
        if value is not None:
            print(f"  ✅ {field}: {type(value).__name__} - {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
        else:
            print(f"  ⚠️ {field}: None (optional)")
    
    # Verify JSONB fields are proper JSON structures
    jsonb_fields = {
        "departments_report": required_mappings["departments_report"],
        "cdatos_reporte": required_mappings["cdatos_reporte"],
        "filter_reporte": required_mappings["filter_reporte"],
        "calculated_reporte": required_mappings["calculated_reporte"]
    }
    
    print("\n✅ JSONB field verification:")
    for field, value in jsonb_fields.items():
        try:
            json_str = json.dumps(value)
            print(f"  ✅ {field}: Valid JSON ({len(json_str)} chars)")
        except Exception as e:
            print(f"  ❌ {field}: Invalid JSON - {e}")
            return False
    
    return True

def main():
    """Run all tests"""
    print("🚀 Testing Table Schema and Data Compatibility\n")
    print("="*60)
    
    tests = [
        ("JSON Structure Compatibility", test_json_structure_compatibility),
        ("Table Creation", test_table_creation),
        ("Data Insertion", test_data_insertion)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Testing: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print('='*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The table schema can handle the sample JSON data.")
        return True
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
